# Nuxt Hello World App

A minimal Nuxt.js application with TypeScript, ESLint, and E2E testing using Cypress.

## 📋 Table of Contents

- [Prerequisites](#prerequisites)
- [Local Development](#local-development)
- [Docker Development](#docker-development)
- [Testing](#testing)
- [Production](#production)
- [Available Scripts](#available-scripts)

## 🔧 Prerequisites

### For Local Development

- **Bun** (recommended) or Node.js 18+
- **Git**

### For E2E Testing (Local)

- All local development prerequisites
- **Cypress dependencies** (automatically installed)

### For Docker Development

- **Docker** and **Docker Compose**

## 🚀 Local Development

### 1. Clone and Install

```bash
# Clone the repository
git clone <repository-url>
cd web-hello-world

# Install dependencies
bun install
```

### 2. Start Development Server

```bash
# Start the development server on http://localhost:3000
bun run dev

# Alternative package managers
npm run dev
pnpm dev
yarn dev
```

### 3. Verify Installation

Open [http://localhost:3000](http://localhost:3000) in your browser to see the application.

## 🐳 Docker Development

### Quick Start

```bash
# Start both app and database services
bun run docker:up

# Or manually
docker compose up --build

# Stop services
bun run docker:down
```

### Services

- **App**: Available at [http://localhost:3000](http://localhost:3000)
- **E2E Tests**: Runs automatically when using `docker compose`

## 🧪 Testing

### E2E Tests with Cypress

#### Option 1: Using Docker (Recommended)

```bash
# Run E2E tests in Docker (includes all dependencies)
docker compose up --abort-on-container-exit --build --exit-code-from e2e

# Or use the npm script
bun run docker:up
```

#### Option 2: Local E2E Testing

```bash
# 1. Start the development server
bun run dev

# 2. In another terminal, run E2E tests
# Interactive mode (opens Cypress UI)
bun run test:e2e

# Headless mode (CI-friendly)
bun run test:e2e:headless
```

### Linting and Type Checking

```bash
# Run ESLint
bun run lint

# Run TypeScript type checking
bun run typecheck
```

## 🏗️ Production

### Build for Production

```bash
# Build the application
bun run build

# Preview production build locally
bun run preview
```

### Docker Production Build

```bash
# Build production Docker image
docker build -t nuxt-app .

# Run production container
docker run -p 3000:3000 nuxt-app
```

## 📜 Available Scripts

| Script                      | Description                  |
| --------------------------- | ---------------------------- |
| `bun run dev`               | Start development server     |
| `bun run build`             | Build for production         |
| `bun run preview`           | Preview production build     |
| `bun run lint`              | Run ESLint                   |
| `bun run typecheck`         | Run TypeScript type checking |
| `bun run test:e2e`          | Run E2E tests (interactive)  |
| `bun run test:e2e:headless` | Run E2E tests (headless)     |
| `bun run docker:up`         | Start Docker services        |
| `bun run docker:down`       | Stop Docker services         |

## 🛠️ Development Notes

### ESLint Configuration

- Unused variables are treated as **warnings** (not errors)
- Configured for Nuxt.js, TypeScript, and Vue.js

### TypeScript

- Strict mode enabled
- Auto-generated types from Nuxt

### Cypress E2E Tests

- Located in `tests/e2e/`
- Configuration in `tests/cypress.config.ts`
- Supports both local and Docker execution

## 📚 Learn More

- [Nuxt.js Documentation](https://nuxt.com/docs)
- [Cypress Documentation](https://docs.cypress.io)
- [Bun Documentation](https://bun.sh/docs)
