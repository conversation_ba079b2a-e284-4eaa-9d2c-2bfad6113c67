# Stage 1: Build stage with Bun
FROM oven/bun:1.2-alpine as builder

WORKDIR /e2e

COPY package.json bun.lock ./
RUN bun install

COPY . .
COPY tests/ ./tests/
COPY tests/cypress.config.ts .

# # FROM oven/bun:1.2-alpine
FROM cypress/included:13.5.1

# Install Bun in the final image
RUN curl -fsSL https://bun.sh/install | bash
ENV PATH="/root/.bun/bin:${PATH}"

WORKDIR /e2e

# Copy installed dependencies from builder
COPY --from=builder /e2e .

CMD ["bun", "test:e2e:headless"]

