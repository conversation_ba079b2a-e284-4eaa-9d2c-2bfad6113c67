# Stage 1: Install Dependencies with Bun
FROM oven/bun:1.2-alpine as builder

WORKDIR /e2e

COPY package.json bun.lock ./
RUN bun install

# Stage 2: Run Cypress
FROM cypress/included:13.5.1

WORKDIR /e2e

# Copy installed dependencies from builder
COPY --from=builder /e2e .

# Copy cypress config and tests
COPY ./tests ./tests
COPY ./tsconfig.json .

ENTRYPOINT [ "cypress", "run", "--config-file=tests/cypress.config.ts" ]



