import { defineConfig } from "cypress";

export default defineConfig({
  e2e: {
    baseUrl: process.env.CYPRESS_baseUrl || "http://localhost:3000",
    specPattern: "tests/e2e/**/*.cy.{js,jsx,ts,tsx}",
    fixturesFolder: "tests/fixtures",
    supportFolder: "tests/support",
    supportFile: "tests/support/e2e.ts",
    video: false,
    screenshotOnRunFailure: false,
    setupNodeEvents(_on, _config) {
      // implement node event listeners here
    },
  },
});
