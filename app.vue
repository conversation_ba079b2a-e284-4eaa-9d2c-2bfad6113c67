<script setup lang="ts">
useHead({
  title: "Hallo World",
});

const loading = ref(false);

const printText = (text: string) => {
  console.log(text);
};

onMounted(() => {
  printText("text");

  loading.value = true;
  setTimeout(() => {
    loading.value = false;
  }, 3000);
});
</script>
<template>
  <div>
    <h1>Hallo World!</h1>
    <div v-if="loading">Loading...</div>
    <p v-else>
      Lorem ipsum dolor sit amet, consectetur adipisicing elit. Ut at magnam vel
      incidunt quaerat praesentium officia iure autem unde est. Quos cum
      consequuntur recusandae et esse accusamus laborum facere id?
    </p>
  </div>
</template>
