<script setup lang="ts">
useHead({
  title: "Hallo World",
});

const loading = ref(false);

const printText = (text: string) => {
  console.log(text);
};

onMounted(() => {
  printText("test");

  loading.value = true;
  setTimeout(() => {
    loading.value = false;
  }, 3000);
});
</script>
<template>
  <div>
    <h1>Hallo World!</h1>
    <div v-if="loading">Loading...</div>
    <div v-else>
      <p>
        Lorem ipsum dolor sit amet, consectetur adipisicing elit. Ut at magnam
        vel incidunt quaerat praesentium officia iure autem unde est. Quos cum
        consequuntur recusandae et esse accusamus laborum facere id?
      </p>
      <p>
        Lorem, ipsum dolor sit amet consectetur adipisicing elit. Nobis hic
        voluptatibus, provident numquam sit blanditiis quidem repellendus ipsum
        nesciunt. Aliquid in quae veritatis asperiores est aliquam error
        eligendi sapiente cum?
      </p>
    </div>
  </div>
</template>
