{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "test:e2e": "cypress open --config-file=tests/cypress.config.ts", "test:e2e:headless": "cypress run --config-file=tests/cypress.config.ts", "docker:up": "docker compose up --build", "docker:down": "docker compose down", "lint": "eslint .", "typecheck": "nuxt typecheck"}, "dependencies": {"@nuxt/eslint": "1.5.2", "eslint": "^9.0.0", "nuxt": "^3.17.6", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"cypress": "^14.5.1", "typescript": "^5.8.3", "vue-tsc": "^3.0.1"}}