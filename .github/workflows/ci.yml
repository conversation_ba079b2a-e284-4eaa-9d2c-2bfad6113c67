name: <PERSON><PERSON> (Lint & Typecheck)

on:
  pull_request:
  push:
    branches: [main, dev]

jobs:
  lint:
    name: Run ESLint
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1

      - name: Install dependencies
        run: bun install

      - name: Run ESLint
        run: bun lint

  typecheck:
    name: Type Check (TypeScript)
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1

      - name: Install dependencies
        run: bun install

      - name: Run TypeScript type check
        run: bun typecheck
